import { mysqlTable, varchar, text, longtext, timestamp, json, int, decimal, uniqueIndex, primaryKey, date, mysqlEnum, index } from "drizzle-orm/mysql-core";
import { randomUUID } from 'crypto';

export const systemSettings = mysqlTable("system_settings", {
  id: varchar("id", { length: 50 }).primaryKey(),
  value: json("value").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

export const web3Profile = mysqlTable("web3Profile", {
  address: varchar("address", { length: 255 }).primaryKey(),
  chain: varchar("chain", { length: 255 }).notNull(),
  name: varchar("name", { length: 255 }), // URL name for the profile
  // bio and compPosition fields removed - now stored in componentPositions table for profilePicture component
  theme: json("theme"), // Store theme preferences as J<PERSON><PERSON>
  role: varchar("role", { length: 50 }).notNull().default("user"), // Role: admin or user
  status: varchar("status", { length: 50 }).notNull().default("new"), // Status: new, in-progress, pending, approved, deleted
  expiryDate: timestamp("expiry_date"), // Expiry date for the profile
  transactionHash: varchar("transaction_hash", { length: 255 }), // Transaction hash for blockchain verification
  referralCode: varchar("referral_code", { length: 8 }), // Referral code (w3txxxxx) - generated when approved
  referredBy: varchar("referred_by", { length: 8 }), // Referral code of who referred this user
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => {
  return {
    // Ensure referral code is unique if provided
    referralCodeIndex: uniqueIndex("referral_code_idx").on(table.referralCode),
  };
});

export const waitingList = mysqlTable("waitingList", {
  address: varchar("address", { length: 255 }).primaryKey(), // Removed foreign key constraint
  chain: varchar("chain", { length: 255 }).notNull(),
  xHandle: text("xHandle").notNull(),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Store component positions for each user's profile
export const componentPositions = mysqlTable("componentPositions", {
  address: varchar("address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }),
  chain: varchar("chain", { length: 255 }).notNull(),
  componentType: varchar("component_type", { length: 50 }).notNull(), // e.g., "banner", "profilePicture", "socialLinks", etc.
  order: varchar("order", { length: 10 }).notNull(), // Order of components (1, 2, 3, etc.)
  hidden: varchar("hidden", { length: 1 }).notNull().default('N'), // Whether the component is hidden (Y/N)
  details: json("details"), // Store component-specific properties as JSON
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.address, table.componentType] }), // Composite primary key
  };
});

// Images table to store all images separately
export const componentImages = mysqlTable("componentImages", {
  id: varchar("id", { length: 36 }).primaryKey().notNull(), // UUID for the image
  address: varchar("address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }),
  componentType: varchar("component_type", { length: 50 }).notNull(), // e.g., "banner", "profilePicture", "hero"
  section: varchar("section", { length: 50 }).default("0"), // Section identifier (e.g., hero section index)
  imageData: longtext("image_data"), // Store base64 encoded image (LONGTEXT)
  scale: decimal("scale", { precision: 20, scale: 16 }).default("1"),
  positionX: int("position_x").default(0),
  positionY: int("position_y").default(0),
  naturalWidth: int("natural_width"),
  naturalHeight: int("natural_height"),
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
});

// Store profile likes
export const profileLikes = mysqlTable("profileLikes", {
  id: varchar("id", { length: 36 }).primaryKey().notNull(), // UUID for the like
  likerAddress: varchar("liker_address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }), // Address of the user who liked
  likedAddress: varchar("liked_address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }), // Address of the profile that was liked
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => {
  return {
    // Ensure a user can only like a profile once
    uniqueLike: uniqueIndex('unique_like_idx').on(table.likerAddress, table.likedAddress),
  };
});

// Store profile referrals
export const profileReferrals = mysqlTable("profileReferrals", {
  id: varchar("id", { length: 36 }).primaryKey().notNull(), // UUID for the referral
  referrerAddress: varchar("referrer_address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }), // Address of the user who referred
  referredAddress: varchar("referred_address", { length: 255 }).notNull().references(() => web3Profile.address, { onDelete: 'cascade' }), // Address of the user who was referred
  referralCode: varchar("referral_code", { length: 8 }).notNull(), // The referral code that was used
  createdAt: timestamp("created_at").notNull().defaultNow(),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => {
  return {
    // Ensure a user can only be referred once
    uniqueReferral: uniqueIndex('unique_referral_idx').on(table.referredAddress),
  };
});

// Token holder snapshots table - stores metadata about each token holder fetch
export const tokenHolderSnapshots = mysqlTable('token_holder_snapshots', {
  id: int('id').primaryKey().autoincrement(),
  contractAddress: varchar('contract_address', { length: 70 }).notNull(), // Increased for Solana addresses
  chainId: varchar('chain_id', { length: 40 }).notNull(), // Increased for Solana chain IDs
  tokenName: varchar('token_name', { length: 100 }),
  snapshotDate: date('snapshot_date').notNull(),
  totalHolders: int('total_holders').notNull(),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => ({
  contractDateIdx: index('contract_date_idx').on(table.contractAddress, table.snapshotDate),
  chainContractIdx: index('chain_contract_idx').on(table.chainId, table.contractAddress),
}));

// Individual token holders table - stores the actual holder data
export const tokenHolders = mysqlTable('token_holders', {
  id: int('id').primaryKey().autoincrement(),
  snapshotId: int('snapshot_id').notNull().references(() => tokenHolderSnapshots.id, { onDelete: 'cascade' }),
  holderAddress: varchar('holder_address', { length: 70 }).notNull(), // Increased for Solana addresses
  balance: varchar('balance', { length: 50 }).notNull(), // Store as string to preserve exact format from CSV
  balanceNumeric: decimal('balance_numeric', { precision: 36, scale: 18 }), // Numeric version for calculations
  pendingBalanceUpdate: varchar('pending_balance_update', { length: 10 }).default('No'),
  createdAt: timestamp('created_at').defaultNow(),
}, (table) => ({
  snapshotHolderIdx: index('snapshot_holder_idx').on(table.snapshotId, table.holderAddress),
  holderAddressIdx: index('holder_address_idx').on(table.holderAddress),
  balanceIdx: index('balance_idx').on(table.balanceNumeric),
}));

// Token holder jobs tracking table - tracks the status of fetch operations
export const tokenHolderJobs = mysqlTable('token_holder_jobs', {
  id: int('id').primaryKey().autoincrement(),
  contractAddress: varchar('contract_address', { length: 70 }).notNull(), // Increased for Solana addresses
  chainId: varchar('chain_id', { length: 40 }).notNull(), // Increased for Solana chain IDs
  tokenName: varchar('token_name', { length: 100 }),
  status: varchar('status', { length: 20 }).default('pending'),
  startedAt: timestamp('started_at'),
  completedAt: timestamp('completed_at'),
  errorMessage: text('error_message'),
  totalHolders: int('total_holders'),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

// Balance check logs table - tracks bulk balance checking operations
export const balanceCheckLogs = mysqlTable('balance_check_logs', {
  id: int('id').primaryKey().autoincrement(),
  jobId: varchar('job_id', { length: 36 }).notNull(), // UUID for the bulk operation
  profileAddress: varchar('profile_address', { length: 255 }).notNull(),
  chainId: varchar('chain_id', { length: 40 }).notNull(),
  oldStatus: varchar('old_status', { length: 50 }).notNull(),
  newStatus: varchar('new_status', { length: 50 }).notNull(),
  balanceCheckResult: json('balance_check_result'), // Store the full balance check result
  tokenRequirements: json('token_requirements'), // Store the token requirements used
  statusChanged: varchar('status_changed', { length: 1 }).notNull().default('N'), // Y/N
  errorMessage: text('error_message'),
  createdAt: timestamp('created_at').defaultNow(),
}, (table) => ({
  jobIdIdx: index('job_id_idx').on(table.jobId),
  profileAddressIdx: index('profile_address_idx').on(table.profileAddress),
  createdAtIdx: index('created_at_idx').on(table.createdAt),
}));

// Sniper creators table - stores creator addresses and names for monitoring
export const sniperCreators = mysqlTable('sniper_creators', {
  id: int('id').primaryKey().autoincrement(),
  address: varchar('address', { length: 70 }).notNull(), // Creator wallet address
  name: varchar('name', { length: 100 }).notNull(), // Creator name/label
  chainId: varchar('chain_id', { length: 40 }).notNull(), // Chain ID
  isActive: varchar('is_active', { length: 1 }).notNull().default('Y'), // Y/N
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => ({
  addressChainIdx: index('address_chain_idx').on(table.address, table.chainId),
  nameIdx: index('name_idx').on(table.name),
}));

// Sniper watched addresses table - stores specific addresses to watch (optional)
export const sniperWatchedAddresses = mysqlTable('sniper_watched_addresses', {
  id: int('id').primaryKey().autoincrement(),
  address: varchar('address', { length: 70 }).notNull(), // Watched wallet address
  name: varchar('name', { length: 100 }), // Optional name/label for the address
  chainId: varchar('chain_id', { length: 40 }).notNull(), // Chain ID
  isActive: varchar('is_active', { length: 1 }).notNull().default('Y'), // Y/N
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => ({
  addressChainIdx: index('address_chain_idx').on(table.address, table.chainId),
  nameIdx: index('name_idx').on(table.name),
}));

// Sniper configurations table - stores sniper settings per user/chain
export const sniperConfigs = mysqlTable('sniper_configs', {
  id: int('id').primaryKey().autoincrement(),
  userAddress: varchar('user_address', { length: 70 }).notNull(), // User who owns this config
  chainId: varchar('chain_id', { length: 40 }).notNull(), // Chain ID
  privateKey: varchar('private_key', { length: 200 }), // Encrypted private key
  buyAmount: varchar('buy_amount', { length: 20 }).notNull().default('10'), // Amount to buy with
  slippage: varchar('slippage', { length: 10 }).notNull().default('5'), // Slippage tolerance
  autoBuyEnabled: varchar('auto_buy_enabled', { length: 1 }).notNull().default('N'), // Y/N
  selectedCreators: json('selected_creators'), // Array of creator IDs to monitor
  selectedWatchedAddresses: json('selected_watched_addresses'), // Array of watched address IDs
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
}, (table) => ({
  userChainIdx: uniqueIndex('user_chain_idx').on(table.userAddress, table.chainId),
}));

// Sniper logs table - tracks sniper monitoring activities and debugging info
export const sniperLogs = mysqlTable('sniper_logs', {
  id: int('id').primaryKey().autoincrement(),
  sessionId: varchar('session_id', { length: 36 }).notNull(), // UUID for monitoring session
  userAddress: varchar('user_address', { length: 70 }).notNull(), // User running the sniper
  chainId: varchar('chain_id', { length: 40 }).notNull(),
  logLevel: varchar('log_level', { length: 10 }).notNull(), // INFO, DEBUG, ERROR, SUCCESS
  logType: varchar('log_type', { length: 30 }).notNull(), // TRANSACTION_SCAN, TOKEN_CREATION, AUTO_BUY, etc.
  message: text('message').notNull(),
  transactionHash: varchar('transaction_hash', { length: 66 }), // Optional tx hash
  tokenAddress: varchar('token_address', { length: 70 }), // Optional token address
  watchedAddress: varchar('watched_address', { length: 70 }), // Address being monitored
  metadata: json('metadata'), // Additional structured data
  createdAt: timestamp('created_at').defaultNow(),
}, (table) => ({
  sessionIdx: index('session_idx').on(table.sessionId),
  userIdx: index('user_idx').on(table.userAddress),
  logTypeIdx: index('log_type_idx').on(table.logType),
  createdAtIdx: index('created_at_idx').on(table.createdAt),
}));

// Sniper transactions table - stores all monitored transactions with analysis results
export const sniperTransactions = mysqlTable('sniper_transactions', {
  id: int('id').primaryKey().autoincrement(),
  sessionId: varchar('session_id', { length: 36 }).notNull(), // UUID for monitoring session
  userAddress: varchar('user_address', { length: 70 }).notNull(), // User running the sniper
  chainId: varchar('chain_id', { length: 40 }).notNull(),
  transactionHash: varchar('transaction_hash', { length: 66 }).notNull(),
  blockNumber: int('block_number').notNull(),
  timestamp: int('timestamp').notNull(), // Unix timestamp
  fromAddress: varchar('from_address', { length: 70 }).notNull(),
  toAddress: varchar('to_address', { length: 70 }),
  value: varchar('value', { length: 50 }).notNull(), // Transaction value in wei
  transactionType: varchar('transaction_type', { length: 20 }).notNull(), // CREATE, BUY, SELL, ADD_LP, REMOVE_LP, TRANSFER
  isTokenCreation: varchar('is_token_creation', { length: 1 }).notNull().default('N'), // Y/N
  createdTokenAddress: varchar('created_token_address', { length: 70 }), // If token creation
  tokenAddress: varchar('token_address', { length: 70 }), // Token involved in transaction
  tokenName: varchar('token_name', { length: 100 }),
  tokenSymbol: varchar('token_symbol', { length: 20 }),
  creatorName: varchar('creator_name', { length: 100 }), // Name of the creator being monitored
  watchedAddress: varchar('watched_address', { length: 70 }), // Which watched address triggered this
  watchedAddressName: varchar('watched_address_name', { length: 100 }), // Name of watched address
  autoBuyEnabled: varchar('auto_buy_enabled', { length: 1 }).notNull().default('N'), // Y/N
  autoBuyTriggered: varchar('auto_buy_triggered', { length: 1 }).notNull().default('N'), // Y/N
  autoBuyTransactionHash: varchar('auto_buy_transaction_hash', { length: 66 }), // Hash of autobuy tx
  autoBuyStatus: varchar('auto_buy_status', { length: 20 }), // SUCCESS, FAILED, PENDING, NOT_TRIGGERED
  parametersChecked: json('parameters_checked'), // JSON of parameter validation results
  parametersMet: varchar('parameters_met', { length: 1 }).notNull().default('N'), // Y/N - overall result
  confidence: int('confidence').notNull().default(0), // 0-100 confidence in analysis
  swapDetails: json('swap_details'), // Swap-specific data
  liquidityDetails: json('liquidity_details'), // LP-specific data
  rawLogs: json('raw_logs'), // Raw transaction logs for debugging
  createdAt: timestamp('created_at').defaultNow(),
}, (table) => ({
  sessionIdx: index('session_idx').on(table.sessionId),
  userIdx: index('user_idx').on(table.userAddress),
  chainIdx: index('chain_idx').on(table.chainId),
  txHashIdx: index('tx_hash_idx').on(table.transactionHash),
  blockIdx: index('block_idx').on(table.blockNumber),
  timestampIdx: index('timestamp_idx').on(table.timestamp),
  typeIdx: index('type_idx').on(table.transactionType),
  tokenCreationIdx: index('token_creation_idx').on(table.isTokenCreation),
  watchedAddressIdx: index('watched_address_idx').on(table.watchedAddress),
  autoBuyIdx: index('auto_buy_idx').on(table.autoBuyTriggered),
  createdAtIdx: index('created_at_idx').on(table.createdAt),
}));
