import { createPublicClient, http, formatUnits } from 'viem';
import { cronos } from 'viem/chains';
import { SniperLogger } from '@/lib/sniperLogger';

export interface AutoBuyValidationParams {
  tokenAddress: string;
  chainId: string;
  minLiquidity?: number; // Minimum liquidity in native token
  maxBuyAmount?: number; // Maximum buy amount in native token
  minHolders?: number; // Minimum number of holders
  maxSlippage?: number; // Maximum allowed slippage
  blacklistedAddresses?: string[]; // Addresses to avoid
  requiresLiquidity?: boolean; // Whether liquidity is required
  requiresVerification?: boolean; // Whether token verification is required
}

export interface ValidationResult {
  isValid: boolean;
  score: number; // 0-100 validation score
  checks: {
    [key: string]: {
      passed: boolean;
      value?: any;
      reason: string;
      weight: number; // How important this check is (0-1)
    };
  };
  warnings: string[];
  errors: string[];
}

export class AutoBuyValidator {
  private publicClient: any;
  private logger?: SniperLogger;

  constructor(chainId: string, logger?: SniperLogger) {
    // For now, we'll use Cronos - this should be made dynamic based on chainId
    this.publicClient = createPublicClient({
      chain: cronos,
      transport: http()
    });
    this.logger = logger;
  }

  async validateToken(params: AutoBuyValidationParams): Promise<ValidationResult> {
    const result: ValidationResult = {
      isValid: false,
      score: 0,
      checks: {},
      warnings: [],
      errors: []
    };

    try {
      if (this.logger) {
        await this.logger.info('AUTO_BUY_VALIDATION', 'Starting token validation', {
          tokenAddress: params.tokenAddress,
          metadata: { params }
        });
      }

      // Check 1: Token contract exists and is valid
      await this.checkTokenContract(params.tokenAddress, result);

      // Check 2: Liquidity validation
      if (params.requiresLiquidity) {
        await this.checkLiquidity(params.tokenAddress, params.minLiquidity || 1, result);
      }

      // Check 3: Holder count validation
      if (params.minHolders) {
        await this.checkHolderCount(params.tokenAddress, params.minHolders, result);
      }

      // Check 4: Blacklist validation
      if (params.blacklistedAddresses && params.blacklistedAddresses.length > 0) {
        await this.checkBlacklist(params.tokenAddress, params.blacklistedAddresses, result);
      }

      // Check 5: Buy amount validation
      if (params.maxBuyAmount) {
        await this.checkBuyAmount(params.maxBuyAmount, result);
      }

      // Check 6: Slippage validation
      if (params.maxSlippage) {
        await this.checkSlippage(params.maxSlippage, result);
      }

      // Calculate overall score and validity
      this.calculateScore(result);

      if (this.logger) {
        await this.logger.info('AUTO_BUY_VALIDATION', 'Token validation completed', {
          tokenAddress: params.tokenAddress,
          metadata: {
            isValid: result.isValid,
            score: result.score,
            checksCount: Object.keys(result.checks).length,
            warningsCount: result.warnings.length,
            errorsCount: result.errors.length
          }
        });
      }

    } catch (error) {
      result.errors.push(`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      
      if (this.logger) {
        await this.logger.error('AUTO_BUY_VALIDATION', 'Token validation error', {
          tokenAddress: params.tokenAddress,
          metadata: {
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        });
      }
    }

    return result;
  }

  private async checkTokenContract(tokenAddress: string, result: ValidationResult): Promise<void> {
    try {
      // Try to get token name and symbol to verify it's a valid ERC20
      const code = await this.publicClient.getBytecode({
        address: tokenAddress as `0x${string}`
      });

      const hasCode = code && code !== '0x';
      
      result.checks.tokenContract = {
        passed: hasCode,
        value: hasCode,
        reason: hasCode ? 'Token contract exists' : 'No contract code found',
        weight: 1.0 // Critical check
      };

      if (!hasCode) {
        result.errors.push('Token contract does not exist or has no code');
      }
    } catch (error) {
      result.checks.tokenContract = {
        passed: false,
        value: false,
        reason: `Error checking contract: ${error instanceof Error ? error.message : 'Unknown error'}`,
        weight: 1.0
      };
      result.errors.push('Failed to verify token contract');
    }
  }

  private async checkLiquidity(tokenAddress: string, minLiquidity: number, result: ValidationResult): Promise<void> {
    try {
      // This is a simplified check - in reality, you'd need to check DEX pairs
      // For now, we'll simulate a liquidity check
      const hasLiquidity = true; // Placeholder
      const liquidityAmount = 10; // Placeholder value

      result.checks.liquidity = {
        passed: liquidityAmount >= minLiquidity,
        value: liquidityAmount,
        reason: `Liquidity: ${liquidityAmount} (min: ${minLiquidity})`,
        weight: 0.8
      };

      if (liquidityAmount < minLiquidity) {
        result.warnings.push(`Low liquidity: ${liquidityAmount} < ${minLiquidity}`);
      }
    } catch (error) {
      result.checks.liquidity = {
        passed: false,
        value: 0,
        reason: `Error checking liquidity: ${error instanceof Error ? error.message : 'Unknown error'}`,
        weight: 0.8
      };
      result.warnings.push('Could not verify liquidity');
    }
  }

  private async checkHolderCount(tokenAddress: string, minHolders: number, result: ValidationResult): Promise<void> {
    try {
      // This would require querying holder data - placeholder for now
      const holderCount = 50; // Placeholder

      result.checks.holderCount = {
        passed: holderCount >= minHolders,
        value: holderCount,
        reason: `Holders: ${holderCount} (min: ${minHolders})`,
        weight: 0.6
      };

      if (holderCount < minHolders) {
        result.warnings.push(`Low holder count: ${holderCount} < ${minHolders}`);
      }
    } catch (error) {
      result.checks.holderCount = {
        passed: false,
        value: 0,
        reason: `Error checking holders: ${error instanceof Error ? error.message : 'Unknown error'}`,
        weight: 0.6
      };
      result.warnings.push('Could not verify holder count');
    }
  }

  private async checkBlacklist(tokenAddress: string, blacklistedAddresses: string[], result: ValidationResult): Promise<void> {
    const isBlacklisted = blacklistedAddresses.some(addr => 
      addr.toLowerCase() === tokenAddress.toLowerCase()
    );

    result.checks.blacklist = {
      passed: !isBlacklisted,
      value: isBlacklisted,
      reason: isBlacklisted ? 'Token is blacklisted' : 'Token not in blacklist',
      weight: 1.0 // Critical check
    };

    if (isBlacklisted) {
      result.errors.push('Token is in blacklist');
    }
  }

  private async checkBuyAmount(maxBuyAmount: number, result: ValidationResult): Promise<void> {
    // This would check against user's balance and configured limits
    const isValidAmount = maxBuyAmount > 0 && maxBuyAmount <= 1000; // Example limits

    result.checks.buyAmount = {
      passed: isValidAmount,
      value: maxBuyAmount,
      reason: isValidAmount ? 'Buy amount is valid' : 'Buy amount exceeds limits',
      weight: 0.7
    };

    if (!isValidAmount) {
      result.warnings.push(`Buy amount ${maxBuyAmount} may be too high`);
    }
  }

  private async checkSlippage(maxSlippage: number, result: ValidationResult): Promise<void> {
    const isValidSlippage = maxSlippage >= 0.1 && maxSlippage <= 50; // 0.1% to 50%

    result.checks.slippage = {
      passed: isValidSlippage,
      value: maxSlippage,
      reason: isValidSlippage ? 'Slippage is within acceptable range' : 'Slippage is outside acceptable range',
      weight: 0.5
    };

    if (!isValidSlippage) {
      result.warnings.push(`Slippage ${maxSlippage}% may be too high or too low`);
    }
  }

  private calculateScore(result: ValidationResult): void {
    let totalWeight = 0;
    let weightedScore = 0;

    // Calculate weighted score
    for (const check of Object.values(result.checks)) {
      totalWeight += check.weight;
      if (check.passed) {
        weightedScore += check.weight;
      }
    }

    // Calculate percentage score
    result.score = totalWeight > 0 ? Math.round((weightedScore / totalWeight) * 100) : 0;

    // Determine if valid (must pass all critical checks and have score > 70)
    const criticalChecksPassed = Object.values(result.checks)
      .filter(check => check.weight >= 0.9)
      .every(check => check.passed);

    result.isValid = criticalChecksPassed && result.score >= 70 && result.errors.length === 0;
  }
}
