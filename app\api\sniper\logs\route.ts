import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { sniperLogs } from '@/db/schema';
import { eq, desc, and } from 'drizzle-orm';
import { logSniperActivity } from '@/lib/sniperLogger';

// GET - Fetch sniper logs
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userAddress = searchParams.get('userAddress');
    const sessionId = searchParams.get('sessionId');
    const chainId = searchParams.get('chainId');
    const logType = searchParams.get('logType');
    const logLevel = searchParams.get('logLevel');
    const limit = parseInt(searchParams.get('limit') || '100');

    if (!userAddress) {
      return Response.json(
        { error: 'User address is required' },
        { status: 400 }
      );
    }

    // Build query conditions
    const conditions = [eq(sniperLogs.userAddress, userAddress)];
    
    if (sessionId) {
      conditions.push(eq(sniperLogs.sessionId, sessionId));
    }
    
    if (chainId) {
      conditions.push(eq(sniperLogs.chainId, chainId));
    }
    
    if (logType) {
      conditions.push(eq(sniperLogs.logType, logType));
    }
    
    if (logLevel) {
      conditions.push(eq(sniperLogs.logLevel, logLevel));
    }

    const logs = await db
      .select()
      .from(sniperLogs)
      .where(and(...conditions))
      .orderBy(desc(sniperLogs.createdAt))
      .limit(limit);

    return Response.json({
      success: true,
      logs,
      total: logs.length
    });

  } catch (error) {
    console.error('Error fetching sniper logs:', error);
    return Response.json(
      { error: 'Failed to fetch sniper logs' },
      { status: 500 }
    );
  }
}

// DELETE - Clear sniper logs
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const userAddress = searchParams.get('userAddress');
    const sessionId = searchParams.get('sessionId');

    if (!userAddress) {
      return Response.json(
        { error: 'User address is required' },
        { status: 400 }
      );
    }

    // Build delete conditions
    const conditions = [eq(sniperLogs.userAddress, userAddress)];
    
    if (sessionId) {
      conditions.push(eq(sniperLogs.sessionId, sessionId));
    }

    await db
      .delete(sniperLogs)
      .where(and(...conditions));

    return Response.json({
      success: true,
      message: sessionId 
        ? 'Session logs cleared successfully' 
        : 'All user logs cleared successfully'
    });

  } catch (error) {
    console.error('Error clearing sniper logs:', error);
    return Response.json(
      { error: 'Failed to clear sniper logs' },
      { status: 500 }
    );
  }
}

// POST - Add a new log entry
export async function POST(request: NextRequest) {
  try {
    const logData = await request.json();

    // Validate required fields
    if (!logData.sessionId || !logData.userAddress || !logData.chainId || !logData.logLevel || !logData.logType || !logData.message) {
      return Response.json(
        { error: 'Missing required fields: sessionId, userAddress, chainId, logLevel, logType, message' },
        { status: 400 }
      );
    }

    await logSniperActivity(logData);

    return Response.json({
      success: true,
      message: 'Log entry created successfully'
    });

  } catch (error) {
    console.error('Error creating log entry:', error);
    return Response.json(
      { error: 'Failed to create log entry' },
      { status: 500 }
    );
  }
}
