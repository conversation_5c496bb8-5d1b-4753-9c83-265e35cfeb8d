-- Migration to add sniper logs table
-- This table tracks sniper monitoring activities and debugging information

CREATE TABLE IF NOT EXISTS `sniper_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` varchar(36) NOT NULL COMMENT 'UUID for monitoring session',
  `user_address` varchar(70) NOT NULL COMMENT 'User running the sniper',
  `chain_id` varchar(40) NOT NULL COMMENT 'Chain ID',
  `log_level` varchar(10) NOT NULL COMMENT 'INFO, DEBUG, ERROR, SUCCESS, WARNING',
  `log_type` varchar(30) NOT NULL COMMENT 'Type of log entry',
  `message` text NOT NULL COMMENT 'Log message',
  `transaction_hash` varchar(66) COMMENT 'Optional transaction hash',
  `token_address` varchar(70) COMMENT 'Optional token address',
  `watched_address` varchar(70) COMMENT 'Address being monitored',
  `metadata` json COMMENT 'Additional structured data',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  <PERSON><PERSON>AR<PERSON>Y (`id`),
  <PERSON><PERSON><PERSON> `session_idx` (`session_id`),
  <PERSON><PERSON>Y `user_idx` (`user_address`),
  KEY `log_type_idx` (`log_type`),
  KEY `created_at_idx` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
