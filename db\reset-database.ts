import { config } from "dotenv";
import { db } from './drizzle';
import { sql } from 'drizzle-orm';

config({ path: ".env" });

/**
 * Reset database script
 * This script will:
 * 1. Drop all existing tables
 * WARNING: This will delete all data in the database!
 */
async function resetDatabase() {
  try {
    console.log('Starting database reset...');

    // Drop all tables
    console.log('Dropping all tables...');

    // Drop tables with foreign keys first
    // Drop balance_check_logs table
    await db.execute(sql`DROP TABLE IF EXISTS balance_check_logs`);

    // Drop sniper tables
    await db.execute(sql`DROP TABLE IF EXISTS sniper_transactions`);
    await db.execute(sql`DROP TABLE IF EXISTS sniper_logs`);
    await db.execute(sql`DROP TABLE IF EXISTS sniper_configs`);
    await db.execute(sql`DROP TABLE IF EXISTS sniper_watched_addresses`);
    await db.execute(sql`DROP TABLE IF EXISTS sniper_creators`);

    // Drop token_holders table (has foreign key to token_holder_snapshots)
    await db.execute(sql`DROP TABLE IF EXISTS token_holders`);

    // Drop token_holder_snapshots table
    await db.execute(sql`DROP TABLE IF EXISTS token_holder_snapshots`);

    // Drop token_holder_jobs table
    await db.execute(sql`DROP TABLE IF EXISTS token_holder_jobs`);

    // Drop profileReferrals table (has foreign key to web3Profile)
    await db.execute(sql`DROP TABLE IF EXISTS profileReferrals`);

    // Drop profileLikes table (has foreign key to web3Profile)
    await db.execute(sql`DROP TABLE IF EXISTS profileLikes`);

    // Drop componentImages table (has foreign key to web3Profile)
    await db.execute(sql`DROP TABLE IF EXISTS componentImages`);

    // Drop componentPositions table (has foreign key to web3Profile)
    await db.execute(sql`DROP TABLE IF EXISTS componentPositions`);

    // Drop waitingList table
    await db.execute(sql`DROP TABLE IF EXISTS waitingList`);

    // Drop web3Profile table
    await db.execute(sql`DROP TABLE IF EXISTS web3Profile`);

    // Drop systemSettings table
    await db.execute(sql`DROP TABLE IF EXISTS system_settings`);

    console.log('All tables dropped successfully');

    // Create tables using SQL statements
    console.log('Creating tables using SQL statements...');

    // Create systemSettings table
    await db.execute(sql`
      CREATE TABLE system_settings (
        id VARCHAR(50) PRIMARY KEY,
        value JSON NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create web3Profile table
    await db.execute(sql`
      CREATE TABLE web3Profile (
        address VARCHAR(255) PRIMARY KEY,
        chain VARCHAR(255) NOT NULL,
        name VARCHAR(255),
        theme JSON,
        role VARCHAR(50) NOT NULL DEFAULT 'user',
        status VARCHAR(50) NOT NULL DEFAULT 'new',
        expiry_date TIMESTAMP NULL,
        transaction_hash VARCHAR(255) NULL,
        referral_code VARCHAR(8) NULL,
        referred_by VARCHAR(8) NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY referral_code_idx (referral_code)
      )
    `);

    // Create waitingList table
    await db.execute(sql`
      CREATE TABLE waitingList (
        address VARCHAR(255) PRIMARY KEY,
        chain VARCHAR(255) NOT NULL,
        xHandle TEXT NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // Create componentPositions table
    await db.execute(sql`
      CREATE TABLE componentPositions (
        address VARCHAR(255) NOT NULL,
        chain VARCHAR(255) NOT NULL,
        component_type VARCHAR(50) NOT NULL,
        \`order\` VARCHAR(10) NOT NULL,
        hidden VARCHAR(1) NOT NULL DEFAULT 'N',
        details JSON NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (address, component_type),
        FOREIGN KEY (address) REFERENCES web3Profile(address) ON DELETE CASCADE
      )
    `);

    // Create componentImages table
    await db.execute(sql`
      CREATE TABLE componentImages (
        id VARCHAR(36) PRIMARY KEY NOT NULL,
        address VARCHAR(255) NOT NULL,
        component_type VARCHAR(50) NOT NULL,
        section VARCHAR(50) DEFAULT '0',
        image_data LONGTEXT NULL,
        scale DECIMAL(20,16) DEFAULT 1,
        position_x INT DEFAULT 0,
        position_y INT DEFAULT 0,
        natural_width INT NULL,
        natural_height INT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (address) REFERENCES web3Profile(address) ON DELETE CASCADE
      )
    `);

    // Create profileLikes table
    await db.execute(sql`
      CREATE TABLE profileLikes (
        id VARCHAR(36) PRIMARY KEY NOT NULL,
        liker_address VARCHAR(255) NOT NULL,
        liked_address VARCHAR(255) NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (liker_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
        FOREIGN KEY (liked_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
        UNIQUE KEY unique_like_idx (liker_address, liked_address)
      )
    `);

    // Create profileReferrals table
    await db.execute(sql`
      CREATE TABLE profileReferrals (
        id VARCHAR(36) PRIMARY KEY NOT NULL,
        referrer_address VARCHAR(255) NOT NULL,
        referred_address VARCHAR(255) NOT NULL,
        referral_code VARCHAR(8) NOT NULL,
        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (referrer_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
        FOREIGN KEY (referred_address) REFERENCES web3Profile(address) ON DELETE CASCADE,
        UNIQUE KEY unique_referral_idx (referred_address)
      )
    `);

    // Create token holder tables
    console.log('Creating token holder tables...');

    // Create token_holder_snapshots table
    await db.execute(sql`
      CREATE TABLE token_holder_snapshots (
        id INT NOT NULL AUTO_INCREMENT,
        contract_address VARCHAR(70) NOT NULL,
        chain_id VARCHAR(40) NOT NULL,
        token_name VARCHAR(100),
        snapshot_date DATE NOT NULL,
        total_holders INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY contract_date_idx (contract_address, snapshot_date),
        KEY chain_contract_idx (chain_id, contract_address)
      )
    `);

    // Create token_holders table
    await db.execute(sql`
      CREATE TABLE token_holders (
        id INT NOT NULL AUTO_INCREMENT,
        snapshot_id INT NOT NULL,
        holder_address VARCHAR(70) NOT NULL,
        balance VARCHAR(50) NOT NULL,
        balance_numeric DECIMAL(36,18),
        pending_balance_update VARCHAR(10) DEFAULT 'No',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY snapshot_holder_idx (snapshot_id, holder_address),
        KEY holder_address_idx (holder_address),
        KEY balance_idx (balance_numeric),
        FOREIGN KEY (snapshot_id) REFERENCES token_holder_snapshots(id) ON DELETE CASCADE
      )
    `);

    // Create token_holder_jobs table
    await db.execute(sql`
      CREATE TABLE token_holder_jobs (
        id INT NOT NULL AUTO_INCREMENT,
        contract_address VARCHAR(70) NOT NULL,
        chain_id VARCHAR(40) NOT NULL,
        token_name VARCHAR(100),
        status VARCHAR(20) DEFAULT 'pending',
        started_at TIMESTAMP NULL,
        completed_at TIMESTAMP NULL,
        error_message TEXT,
        total_holders INT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id)
      )
    `);

    // Create balance_check_logs table
    await db.execute(sql`
      CREATE TABLE balance_check_logs (
        id INT NOT NULL AUTO_INCREMENT,
        job_id VARCHAR(36) NOT NULL,
        profile_address VARCHAR(255) NOT NULL,
        chain_id VARCHAR(40) NOT NULL,
        old_status VARCHAR(50) NOT NULL,
        new_status VARCHAR(50) NOT NULL,
        balance_check_result JSON,
        token_requirements JSON,
        status_changed VARCHAR(1) NOT NULL DEFAULT 'N',
        error_message TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY job_id_idx (job_id),
        KEY profile_address_idx (profile_address),
        KEY created_at_idx (created_at)
      )
    `);

    // Create sniper tables
    console.log('Creating sniper tables...');

    // Create sniper_creators table
    await db.execute(sql`
      CREATE TABLE sniper_creators (
        id INT NOT NULL AUTO_INCREMENT,
        address VARCHAR(70) NOT NULL,
        name VARCHAR(100) NOT NULL,
        chain_id VARCHAR(40) NOT NULL,
        is_active VARCHAR(1) NOT NULL DEFAULT 'Y',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY address_chain_idx (address, chain_id),
        KEY name_idx (name)
      )
    `);

    // Create sniper_watched_addresses table
    await db.execute(sql`
      CREATE TABLE sniper_watched_addresses (
        id INT NOT NULL AUTO_INCREMENT,
        address VARCHAR(70) NOT NULL,
        name VARCHAR(100),
        chain_id VARCHAR(40) NOT NULL,
        is_active VARCHAR(1) NOT NULL DEFAULT 'Y',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY address_chain_idx (address, chain_id),
        KEY name_idx (name)
      )
    `);

    // Create sniper_configs table
    await db.execute(sql`
      CREATE TABLE sniper_configs (
        id INT NOT NULL AUTO_INCREMENT,
        user_address VARCHAR(70) NOT NULL,
        chain_id VARCHAR(40) NOT NULL,
        private_key VARCHAR(200),
        buy_amount VARCHAR(20) NOT NULL DEFAULT '10',
        slippage VARCHAR(10) NOT NULL DEFAULT '5',
        auto_buy_enabled VARCHAR(1) NOT NULL DEFAULT 'N',
        selected_creators JSON,
        selected_watched_addresses JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY user_chain_idx (user_address, chain_id)
      )
    `);

    // Create sniper_logs table
    await db.execute(sql`
      CREATE TABLE sniper_logs (
        id INT NOT NULL AUTO_INCREMENT,
        session_id VARCHAR(36) NOT NULL,
        user_address VARCHAR(70) NOT NULL,
        chain_id VARCHAR(40) NOT NULL,
        log_level VARCHAR(10) NOT NULL,
        log_type VARCHAR(30) NOT NULL,
        message TEXT NOT NULL,
        transaction_hash VARCHAR(66),
        token_address VARCHAR(70),
        watched_address VARCHAR(70),
        metadata JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY session_idx (session_id),
        KEY user_idx (user_address),
        KEY log_type_idx (log_type),
        KEY created_at_idx (created_at)
      )
    `);

    // Create sniper_transactions table
    await db.execute(sql`
      CREATE TABLE sniper_transactions (
        id INT NOT NULL AUTO_INCREMENT,
        session_id VARCHAR(36) NOT NULL,
        user_address VARCHAR(70) NOT NULL,
        chain_id VARCHAR(40) NOT NULL,
        transaction_hash VARCHAR(66) NOT NULL,
        block_number INT NOT NULL,
        timestamp INT NOT NULL,
        from_address VARCHAR(70) NOT NULL,
        to_address VARCHAR(70),
        value VARCHAR(50) NOT NULL,
        transaction_type VARCHAR(20) NOT NULL,
        is_token_creation VARCHAR(1) NOT NULL DEFAULT 'N',
        created_token_address VARCHAR(70),
        token_address VARCHAR(70),
        token_name VARCHAR(100),
        token_symbol VARCHAR(20),
        creator_name VARCHAR(100),
        watched_address VARCHAR(70),
        watched_address_name VARCHAR(100),
        auto_buy_enabled VARCHAR(1) NOT NULL DEFAULT 'N',
        auto_buy_triggered VARCHAR(1) NOT NULL DEFAULT 'N',
        auto_buy_transaction_hash VARCHAR(66),
        auto_buy_status VARCHAR(20),
        parameters_checked JSON,
        parameters_met VARCHAR(1) NOT NULL DEFAULT 'N',
        confidence INT NOT NULL DEFAULT 0,
        swap_details JSON,
        liquidity_details JSON,
        raw_logs JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY session_idx (session_id),
        KEY user_idx (user_address),
        KEY chain_idx (chain_id),
        KEY tx_hash_idx (transaction_hash),
        KEY block_idx (block_number),
        KEY timestamp_idx (timestamp),
        KEY type_idx (transaction_type),
        KEY token_creation_idx (is_token_creation),
        KEY watched_address_idx (watched_address),
        KEY auto_buy_idx (auto_buy_triggered),
        KEY created_at_idx (created_at)
      )
    `);

    console.log('All tables created successfully');

    // Run additional setup scripts
    console.log('Running additional setup scripts...');

    // Import and run add-system-settings
    const { default: addSystemSettings } = await import('./add-system-settings');
    const settingsAdded = await addSystemSettings();

    if (!settingsAdded) {
      throw new Error('Failed to add system settings');
    }

    // Ensure no test profiles are created
    console.log('Database reset completed with clean tables - no test profiles created');
    console.log('If you need a test profile, run: npm run create-test-profile');

    console.log('Database reset completed successfully');
  } catch (error) {
    console.error('Database reset failed:', error);
    process.exit(1);
  }
}

// Run the reset function
resetDatabase()
  .then(() => {
    console.log('Database reset completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Error during database reset:', error);
    process.exit(1);
  });
