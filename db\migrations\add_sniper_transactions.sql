-- Migration to add sniper transactions table
-- This table stores all monitored transactions with comprehensive analysis results

CREATE TABLE IF NOT EXISTS `sniper_transactions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `session_id` varchar(36) NOT NULL COMMENT 'UUID for monitoring session',
  `user_address` varchar(70) NOT NULL COMMENT 'User running the sniper',
  `chain_id` varchar(40) NOT NULL COMMENT 'Chain ID',
  `transaction_hash` varchar(66) NOT NULL COMMENT 'Transaction hash',
  `block_number` int NOT NULL COMMENT 'Block number',
  `timestamp` int NOT NULL COMMENT 'Unix timestamp',
  `from_address` varchar(70) NOT NULL COMMENT 'Transaction from address',
  `to_address` varchar(70) COMMENT 'Transaction to address',
  `value` varchar(50) NOT NULL COMMENT 'Transaction value in wei',
  `transaction_type` varchar(20) NOT NULL COMMENT 'CREATE, BUY, SELL, ADD_<PERSON>, REMOVE_LP, TRANSFER',
  `is_token_creation` varchar(1) NOT NULL DEFAULT 'N' COMMENT 'Y/N - Is this a token creation',
  `created_token_address` varchar(70) COMMENT 'Address of created token if applicable',
  `token_address` varchar(70) COMMENT 'Token involved in transaction',
  `token_name` varchar(100) COMMENT 'Token name if available',
  `token_symbol` varchar(20) COMMENT 'Token symbol if available',
  `creator_name` varchar(100) COMMENT 'Name of the creator being monitored',
  `watched_address` varchar(70) COMMENT 'Which watched address triggered this',
  `watched_address_name` varchar(100) COMMENT 'Name of watched address',
  `auto_buy_enabled` varchar(1) NOT NULL DEFAULT 'N' COMMENT 'Y/N - Was autobuy enabled',
  `auto_buy_triggered` varchar(1) NOT NULL DEFAULT 'N' COMMENT 'Y/N - Was autobuy triggered',
  `auto_buy_transaction_hash` varchar(66) COMMENT 'Hash of autobuy transaction',
  `auto_buy_status` varchar(20) COMMENT 'SUCCESS, FAILED, PENDING, NOT_TRIGGERED',
  `parameters_checked` json COMMENT 'JSON of parameter validation results',
  `parameters_met` varchar(1) NOT NULL DEFAULT 'N' COMMENT 'Y/N - Overall parameter validation result',
  `confidence` int NOT NULL DEFAULT 0 COMMENT '0-100 confidence in analysis',
  `swap_details` json COMMENT 'Swap-specific data',
  `liquidity_details` json COMMENT 'LP-specific data',
  `raw_logs` json COMMENT 'Raw transaction logs for debugging',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `session_idx` (`session_id`),
  KEY `user_idx` (`user_address`),
  KEY `chain_idx` (`chain_id`),
  KEY `tx_hash_idx` (`transaction_hash`),
  KEY `block_idx` (`block_number`),
  KEY `timestamp_idx` (`timestamp`),
  KEY `type_idx` (`transaction_type`),
  KEY `token_creation_idx` (`is_token_creation`),
  KEY `watched_address_idx` (`watched_address`),
  KEY `auto_buy_idx` (`auto_buy_triggered`),
  KEY `created_at_idx` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
