import { NextRequest } from 'next/server';
import { createPublicClient, http, formatUnits } from 'viem';
import { cronos } from 'viem/chains';
import { SniperLogger } from '@/lib/sniperLogger';
import { randomUUID } from 'crypto';
import { db } from '@/db/drizzle';
import { sniperWatchedAddresses } from '@/db/schema';
import {
  storeTransaction,
  getCreatorInfo,
  getWatchedAddressInfo,
  StoredTransactionData
} from '@/lib/sniperTransactionStorage';

// Create a public client for Cronos chain
const publicClient = createPublicClient({
  chain: cronos,
  transport: http()
});

// Known factory contracts that create tokens
const TOKEN_FACTORY_CONTRACTS = [
  '******************************************', // The factory from your example
  // Add more factory contracts here as you discover them
];

// Known DEX router contracts for buy/sell detection
const DEX_ROUTER_CONTRACTS = [
  '******************************************', // MMF Router
  '******************************************', // Uniswap V2 Router
  '******************************************', // Uniswap V3 Router
  // Add more DEX routers as needed
];

// Known liquidity pool factory contracts
const LP_FACTORY_CONTRACTS = [
  '0x5c69bee701ef814a2b6a3edd4b1652cb9cc5aa6f', // Uniswap V2 Factory
  '0x1f98431c8ad98523631ae4a59f267346ea31f984', // Uniswap V3 Factory
  // Add more LP factories as needed
];

// Event signatures for different transaction types
const EVENT_SIGNATURES = {
  TRANSFER: '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef',
  SWAP: '0xd78ad95fa46c994b6551d0da85fc275fe613ce37657fb8d5e3d130840159d822', // Uniswap V2 Swap
  MINT: '0x4c209b5fc8ad50758f13e2e1088ba56a560dff690a1c6fef26394f4c03821c4f', // Uniswap V2 Mint
  BURN: '0xdccd412f0b1252819cb1fd330b93224ca42612892bb3f4f789976e6d81936496', // Uniswap V2 Burn
  PAIR_CREATED: '0x0d3648bd0f6ba80134a33ba9275ac585d9d315f0ad8355cddefde31afa28d0e9', // Uniswap V2 PairCreated
  APPROVAL: '0x8c5be1e5ebec7d5bd14f71427d1e84f3dd0314c0f7b2291e5b200ac8c7c3b925',
};

export type TransactionType = 'CREATE' | 'BUY' | 'SELL' | 'ADD_LP' | 'REMOVE_LP' | 'TRANSFER' | 'APPROVAL' | 'UNKNOWN';

export interface TransactionAnalysis {
  transactionType: TransactionType;
  isTokenCreation: boolean;
  createdTokenAddress?: string;
  tokenAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
  swapDetails?: {
    tokenIn: string;
    tokenOut: string;
    amountIn: string;
    amountOut: string;
  };
  liquidityDetails?: {
    token0: string;
    token1: string;
    amount0: string;
    amount1: string;
    pairAddress?: string;
  };
  logs: any[];
  confidence: number; // 0-100 confidence in the analysis
}

// Enhanced function to analyze transaction and determine its type
async function analyzeTransaction(tx: any, receipt: any, logger?: SniperLogger, watchedAddress?: string): Promise<TransactionAnalysis> {
  const result: TransactionAnalysis = {
    transactionType: 'UNKNOWN',
    isTokenCreation: false,
    logs: [],
    confidence: 0
  };

  if (logger) {
    await logger.debug('TRANSACTION_ANALYSIS', `Starting comprehensive transaction analysis: ${tx.hash}`, {
      transactionHash: tx.hash,
      watchedAddress,
      metadata: {
        transactionTo: tx.to?.toLowerCase(),
        transactionFrom: tx.from?.toLowerCase(),
        value: tx.value?.toString() || '0',
        hasLogs: !!receipt?.logs,
        logCount: receipt?.logs?.length || 0
      }
    });
  }

  if (!receipt?.logs || receipt.logs.length === 0) {
    if (logger) {
      await logger.debug('TRANSACTION_ANALYSIS', `No logs found - likely simple transfer`, {
        transactionHash: tx.hash,
        watchedAddress,
        metadata: {
          hasReceipt: !!receipt,
          logCount: receipt?.logs?.length || 0
        }
      });
    }
    result.transactionType = 'TRANSFER';
    result.confidence = 50;
    return result;
  }

  // Check if transaction is to a known factory contract (token creation)
  const isFactoryTransaction = TOKEN_FACTORY_CONTRACTS.includes(tx.to?.toLowerCase() || '');
  const isDexTransaction = DEX_ROUTER_CONTRACTS.includes(tx.to?.toLowerCase() || '');
  const isLpFactoryTransaction = LP_FACTORY_CONTRACTS.includes(tx.to?.toLowerCase() || '');

  if (logger) {
    await logger.debug('CONTRACT_TYPE_CHECK', `Contract type analysis`, {
      transactionHash: tx.hash,
      watchedAddress,
      metadata: {
        transactionTo: tx.to?.toLowerCase(),
        isFactoryTransaction,
        isDexTransaction,
        isLpFactoryTransaction,
        knownFactories: TOKEN_FACTORY_CONTRACTS,
        knownDexRouters: DEX_ROUTER_CONTRACTS,
        knownLpFactories: LP_FACTORY_CONTRACTS
      }
    });
  }

  // Analyze logs to determine transaction type
  let transferEvents = 0;
  let swapEvents = 0;
  let mintEvents = 0;
  let burnEvents = 0;
  let pairCreatedEvents = 0;
  let approvalEvents = 0;

  for (const log of receipt.logs) {
    if (!log.topics || log.topics.length === 0) continue;

    const eventSignature = log.topics[0];
    result.logs.push(log);

    switch (eventSignature) {
      case EVENT_SIGNATURES.TRANSFER:
        transferEvents++;
        await analyzeTransferEvent(log, result, logger, tx.hash, watchedAddress);
        break;

      case EVENT_SIGNATURES.SWAP:
        swapEvents++;
        await analyzeSwapEvent(log, result, logger, tx.hash, watchedAddress);
        break;

      case EVENT_SIGNATURES.MINT:
        mintEvents++;
        await analyzeMintEvent(log, result, logger, tx.hash, watchedAddress);
        break;

      case EVENT_SIGNATURES.BURN:
        burnEvents++;
        await analyzeBurnEvent(log, result, logger, tx.hash, watchedAddress);
        break;

      case EVENT_SIGNATURES.PAIR_CREATED:
        pairCreatedEvents++;
        await analyzePairCreatedEvent(log, result, logger, tx.hash, watchedAddress);
        break;

      case EVENT_SIGNATURES.APPROVAL:
        approvalEvents++;
        break;
    }
  }

  // Determine transaction type based on events and contract interactions
  if (pairCreatedEvents > 0 || (isLpFactoryTransaction && mintEvents > 0)) {
    result.transactionType = 'ADD_LP';
    result.confidence = 90;
  } else if (burnEvents > 0 && transferEvents > 0) {
    result.transactionType = 'REMOVE_LP';
    result.confidence = 85;
  } else if (result.isTokenCreation || (isFactoryTransaction && transferEvents > 0)) {
    result.transactionType = 'CREATE';
    result.confidence = 95;
  } else if (swapEvents > 0 || (isDexTransaction && transferEvents >= 2)) {
    // Determine if it's a buy or sell based on token flow
    result.transactionType = await determineBuyOrSell(receipt.logs, tx, logger, watchedAddress);
    result.confidence = 80;
  } else if (transferEvents > 0) {
    result.transactionType = 'TRANSFER';
    result.confidence = 70;
  }

  if (logger) {
    await logger.info('TRANSACTION_ANALYSIS', `Transaction analysis complete`, {
      transactionHash: tx.hash,
      watchedAddress,
      metadata: {
        transactionType: result.transactionType,
        confidence: result.confidence,
        isTokenCreation: result.isTokenCreation,
        eventCounts: {
          transfers: transferEvents,
          swaps: swapEvents,
          mints: mintEvents,
          burns: burnEvents,
          pairCreated: pairCreatedEvents,
          approvals: approvalEvents
        }
      }
    });
  }

  return result;
}

// Helper function to analyze Transfer events
async function analyzeTransferEvent(log: any, result: TransactionAnalysis, logger?: SniperLogger, txHash?: string, watchedAddress?: string) {
  const fromAddress = log.topics[1];
  const toAddress = log.topics[2];
  const nullAddress = '0x0000000000000000000000000000000000000000000000000000000000000000';

  // Check if it's a mint (from null address) - indicates token creation
  if (fromAddress === nullAddress) {
    result.isTokenCreation = true;
    result.createdTokenAddress = log.address;
    result.tokenAddress = log.address;

    if (logger) {
      await logger.success('TOKEN_CREATION_DETECTED', `Token creation detected via Transfer from null address`, {
        transactionHash: txHash,
        tokenAddress: log.address,
        watchedAddress,
        metadata: {
          transferLog: log,
          toAddress
        }
      });
    }
  }

  if (logger) {
    await logger.debug('TRANSFER_EVENT_ANALYSIS', `Transfer event analyzed`, {
      transactionHash: txHash,
      tokenAddress: log.address,
      watchedAddress,
      metadata: {
        fromAddress,
        toAddress,
        isFromNullAddress: fromAddress === nullAddress,
        isMint: fromAddress === nullAddress
      }
    });
  }
}

// Helper function to analyze Swap events
async function analyzeSwapEvent(log: any, result: TransactionAnalysis, logger?: SniperLogger, txHash?: string, watchedAddress?: string) {
  // Swap event typically has: sender, amount0In, amount1In, amount0Out, amount1Out, to
  if (log.data && log.topics) {
    result.swapDetails = {
      tokenIn: '', // Would need to decode the data to get exact tokens
      tokenOut: '',
      amountIn: '',
      amountOut: ''
    };

    if (logger) {
      await logger.debug('SWAP_EVENT_ANALYSIS', `Swap event detected`, {
        transactionHash: txHash,
        tokenAddress: log.address,
        watchedAddress,
        metadata: {
          swapLog: log
        }
      });
    }
  }
}

// Helper function to analyze Mint events (LP addition)
async function analyzeMintEvent(log: any, result: TransactionAnalysis, logger?: SniperLogger, txHash?: string, watchedAddress?: string) {
  if (logger) {
    await logger.debug('MINT_EVENT_ANALYSIS', `Mint event detected - likely LP addition`, {
      transactionHash: txHash,
      tokenAddress: log.address,
      watchedAddress,
      metadata: {
        mintLog: log
      }
    });
  }
}

// Helper function to analyze Burn events (LP removal)
async function analyzeBurnEvent(log: any, result: TransactionAnalysis, logger?: SniperLogger, txHash?: string, watchedAddress?: string) {
  if (logger) {
    await logger.debug('BURN_EVENT_ANALYSIS', `Burn event detected - likely LP removal`, {
      transactionHash: txHash,
      tokenAddress: log.address,
      watchedAddress,
      metadata: {
        burnLog: log
      }
    });
  }
}

// Helper function to analyze PairCreated events
async function analyzePairCreatedEvent(log: any, result: TransactionAnalysis, logger?: SniperLogger, txHash?: string, watchedAddress?: string) {
  if (logger) {
    await logger.success('PAIR_CREATED_DETECTED', `New trading pair created`, {
      transactionHash: txHash,
      tokenAddress: log.address,
      watchedAddress,
      metadata: {
        pairCreatedLog: log
      }
    });
  }
}

// Helper function to determine if a transaction is a buy or sell
async function determineBuyOrSell(logs: any[], tx: any, logger?: SniperLogger, watchedAddress?: string): Promise<TransactionType> {
  // This is a simplified logic - in reality, you'd need to analyze the token flow
  // to determine if native token is going in (buy) or coming out (sell)

  // For now, we'll use a heuristic based on transaction value
  const txValue = BigInt(tx.value || '0');

  if (txValue > 0n) {
    // Native token is being sent, likely a buy
    if (logger) {
      await logger.debug('BUY_SELL_ANALYSIS', `Determined as BUY - native token sent`, {
        transactionHash: tx.hash,
        watchedAddress,
        metadata: {
          transactionValue: tx.value?.toString() || '0',
          reasoning: 'Native token value > 0'
        }
      });
    }
    return 'BUY';
  } else {
    // No native token sent, likely a sell
    if (logger) {
      await logger.debug('BUY_SELL_ANALYSIS', `Determined as SELL - no native token sent`, {
        transactionHash: tx.hash,
        watchedAddress,
        metadata: {
          transactionValue: tx.value?.toString() || '0',
          reasoning: 'Native token value = 0'
        }
      });
    }
    return 'SELL';
  }
}

// Enhanced function to check if transaction involves any watched addresses
async function checkWatchedAddressMatches(
  tx: any,
  receipt: any,
  chainId: string,
  logger?: SniperLogger
): Promise<{
  hasMatches: boolean;
  matchedAddresses: Array<{
    address: string;
    name?: string;
    role: 'from' | 'to' | 'token_involved' | 'log_address';
    details?: any;
  }>;
}> {
  const result = {
    hasMatches: false,
    matchedAddresses: [] as Array<{
      address: string;
      name?: string;
      role: 'from' | 'to' | 'token_involved' | 'log_address';
      details?: any;
    }>
  };

  try {
    // Get all active watched addresses for this chain
    const watchedAddresses = await db.select()
      .from(sniperWatchedAddresses)
      .where(
        db.and(
          db.eq(sniperWatchedAddresses.chainId, chainId),
          db.eq(sniperWatchedAddresses.isActive, 'Y')
        )
      );

    if (watchedAddresses.length === 0) {
      return result;
    }

    const watchedAddressMap = new Map(
      watchedAddresses.map(wa => [wa.address.toLowerCase(), { address: wa.address, name: wa.name }])
    );

    // Check transaction from/to addresses
    const txFrom = tx.from?.toLowerCase();
    const txTo = tx.to?.toLowerCase();

    if (txFrom && watchedAddressMap.has(txFrom)) {
      const watchedInfo = watchedAddressMap.get(txFrom)!;
      result.matchedAddresses.push({
        address: watchedInfo.address,
        name: watchedInfo.name || undefined,
        role: 'from',
        details: { transactionValue: tx.value?.toString() || '0' }
      });
      result.hasMatches = true;
    }

    if (txTo && watchedAddressMap.has(txTo)) {
      const watchedInfo = watchedAddressMap.get(txTo)!;
      result.matchedAddresses.push({
        address: watchedInfo.address,
        name: watchedInfo.name || undefined,
        role: 'to',
        details: { transactionValue: tx.value?.toString() || '0' }
      });
      result.hasMatches = true;
    }

    // Check transaction logs for watched addresses
    if (receipt?.logs && Array.isArray(receipt.logs)) {
      for (const log of receipt.logs) {
        // Check log address (contract that emitted the log)
        const logAddress = log.address?.toLowerCase();
        if (logAddress && watchedAddressMap.has(logAddress)) {
          const watchedInfo = watchedAddressMap.get(logAddress)!;
          result.matchedAddresses.push({
            address: watchedInfo.address,
            name: watchedInfo.name || undefined,
            role: 'log_address',
            details: {
              logIndex: log.logIndex,
              topics: log.topics,
              eventSignature: log.topics?.[0]
            }
          });
          result.hasMatches = true;
        }

        // Check addresses in log topics (for Transfer events, etc.)
        if (log.topics && Array.isArray(log.topics)) {
          for (let i = 1; i < log.topics.length; i++) {
            const topic = log.topics[i];
            if (topic && topic.length === 66) { // 0x + 64 hex chars = address in topic
              // Extract address from topic (last 40 chars)
              const addressFromTopic = '0x' + topic.slice(-40);
              const topicAddress = addressFromTopic.toLowerCase();

              if (watchedAddressMap.has(topicAddress)) {
                const watchedInfo = watchedAddressMap.get(topicAddress)!;
                result.matchedAddresses.push({
                  address: watchedInfo.address,
                  name: watchedInfo.name || undefined,
                  role: 'token_involved',
                  details: {
                    logIndex: log.logIndex,
                    topicIndex: i,
                    eventSignature: log.topics[0],
                    contractAddress: log.address
                  }
                });
                result.hasMatches = true;
              }
            }
          }
        }
      }
    }

    // Remove duplicates based on address and role
    const uniqueMatches = result.matchedAddresses.filter((match, index, self) =>
      index === self.findIndex(m => m.address === match.address && m.role === match.role)
    );
    result.matchedAddresses = uniqueMatches;

    if (result.hasMatches && logger) {
      await logger.success('WATCHED_ADDRESS_MATCH', `Transaction involves watched addresses`, {
        transactionHash: tx.hash,
        metadata: {
          matchedAddresses: result.matchedAddresses,
          transactionFrom: txFrom,
          transactionTo: txTo,
          totalMatches: result.matchedAddresses.length,
          uniqueAddresses: [...new Set(result.matchedAddresses.map(m => m.address))].length
        }
      });
    }

  } catch (error) {
    if (logger) {
      await logger.error('ERROR', `Error checking watched address matches`, {
        transactionHash: tx.hash,
        metadata: {
          error: error instanceof Error ? error.message : 'Unknown error',
          chainId
        }
      });
    }
  }

  return result;
}

interface Transaction {
  hash: string;
  from: string;
  to: string;
  value: string;
  timestamp: number;
  blockNumber: number;
  transactionType?: TransactionType;
  isTokenCreation?: boolean;
  createdTokenAddress?: string;
  tokenAddress?: string;
  tokenName?: string;
  tokenSymbol?: string;
  swapDetails?: {
    tokenIn: string;
    tokenOut: string;
    amountIn: string;
    amountOut: string;
  };
  liquidityDetails?: {
    token0: string;
    token1: string;
    amount0: string;
    amount1: string;
    pairAddress?: string;
  };
  confidence?: number;
  logs?: any[];
}

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');
    const chainId = searchParams.get('chainId') || '25';
    const limit = parseInt(searchParams.get('limit') || '5');
    const userAddress = searchParams.get('userAddress'); // Optional for logging
    const sessionId = searchParams.get('sessionId') || randomUUID(); // Create session if not provided

    if (!address) {
      return Response.json(
        { error: 'Address parameter is required' },
        { status: 400 }
      );
    }

    // Validate address format
    if (!address.match(/^0x[a-fA-F0-9]{40}$/)) {
      return Response.json(
        { error: 'Invalid address format' },
        { status: 400 }
      );
    }

    // Create logger if userAddress is provided
    let logger: SniperLogger | undefined;
    if (userAddress) {
      logger = new SniperLogger(sessionId, userAddress, chainId);
      await logger.info('TRANSACTION_SCAN', `Starting transaction scan for address: ${address}`, {
        watchedAddress: address,
        metadata: {
          limit,
          chainId
        }
      });
    }

    // Get the latest block number
    const latestBlock = await publicClient.getBlockNumber();

    if (logger) {
      await logger.debug('TRANSACTION_SCAN', `Latest block: ${latestBlock}`, {
        watchedAddress: address,
        metadata: {
          latestBlock: Number(latestBlock)
        }
      });
    }

    // We'll check the last 100 blocks for transactions (more reasonable for RPC limits)
    const blocksToCheck = 100n;
    const startBlock = latestBlock - blocksToCheck;

    const transactions: Transaction[] = [];
    let foundTransactions = 0;
    let blocksChecked = 0;

    // Check blocks in reverse order (newest first), but limit to avoid RPC timeouts
    for (let blockNum = latestBlock; blockNum > startBlock && foundTransactions < limit && blocksChecked < 50; blockNum--) {
      try {
        blocksChecked++;

        const block = await publicClient.getBlock({
          blockNumber: blockNum,
          includeTransactions: true
        });

        if (block.transactions && Array.isArray(block.transactions)) {
          for (const tx of block.transactions) {
            // Check if transaction involves the target address
            if (typeof tx === 'object' && tx !== null && 'from' in tx && 'to' in tx) {
              const txFrom = tx.from?.toLowerCase();
              const txTo = tx.to?.toLowerCase();
              const targetAddr = address.toLowerCase();

              if (txFrom === targetAddr || txTo === targetAddr) {
                if (logger) {
                  await logger.debug('TRANSACTION_SCAN', `Found transaction involving target address`, {
                    transactionHash: tx.hash,
                    watchedAddress: address,
                    metadata: {
                      from: txFrom,
                      to: txTo,
                      target: targetAddr,
                      value: tx.value?.toString() || '0'
                    }
                  });
                }

                // Get transaction receipt to analyze transaction type and details
                let transactionAnalysis: TransactionAnalysis = {
                  transactionType: 'UNKNOWN',
                  isTokenCreation: false,
                  logs: [],
                  confidence: 0
                };

                try {
                  const receipt = await publicClient.getTransactionReceipt({
                    hash: tx.hash as `0x${string}`
                  });
                  transactionAnalysis = await analyzeTransaction(tx, receipt, logger, address);
                } catch (receiptError) {
                  if (logger) {
                    await logger.error('ERROR', `Error getting receipt for transaction`, {
                      transactionHash: tx.hash,
                      watchedAddress: address,
                      metadata: {
                        error: receiptError instanceof Error ? receiptError.message : 'Unknown error'
                      }
                    });
                  }
                }

                // Check if this transaction involves a creator or watched address
                const creatorInfo = await getCreatorInfo(txFrom, chainId) || await getCreatorInfo(txTo || '', chainId);

                // Enhanced watched address matching
                const watchedMatches = await checkWatchedAddressMatches(tx, receipt, chainId, logger);
                const primaryWatchedAddress = watchedMatches.matchedAddresses.length > 0 ? watchedMatches.matchedAddresses[0] : null;

                // Store transaction in database if we have logging enabled
                if (userAddress) {
                  const storedTransactionData: StoredTransactionData = {
                    sessionId,
                    userAddress,
                    chainId,
                    transactionHash: tx.hash || '',
                    blockNumber: Number(blockNum),
                    timestamp: Number(block.timestamp),
                    fromAddress: txFrom,
                    toAddress: txTo || undefined,
                    value: tx.value?.toString() || '0',
                    transactionAnalysis,
                    creatorInfo: creatorInfo || undefined,
                    watchedAddressInfo: primaryWatchedAddress ? {
                      name: primaryWatchedAddress.name,
                      address: primaryWatchedAddress.address
                    } : undefined,
                    autoBuyConfig: {
                      enabled: false, // Will be determined later based on user config
                      triggered: false,
                      status: 'NOT_TRIGGERED'
                    },
                    parametersChecked: {
                      watchedAddressMatch: {
                        checked: true,
                        value: watchedMatches.matchedAddresses.length,
                        met: watchedMatches.hasMatches,
                        reason: watchedMatches.hasMatches
                          ? `Found ${watchedMatches.matchedAddresses.length} watched address matches`
                          : 'No watched addresses involved'
                      }
                    }
                  };

                  const storedId = await storeTransaction(storedTransactionData);

                  if (logger && storedId) {
                    await logger.info('TRANSACTION_SCAN', `Transaction stored in database`, {
                      transactionHash: tx.hash,
                      watchedAddress: address,
                      metadata: {
                        storedId,
                        transactionType: transactionAnalysis.transactionType,
                        isTokenCreation: transactionAnalysis.isTokenCreation,
                        hasCreatorInfo: !!creatorInfo,
                        watchedAddressMatches: watchedMatches.matchedAddresses.length,
                        watchedAddressDetails: watchedMatches.matchedAddresses
                      }
                    });
                  }
                }

                transactions.push({
                  hash: tx.hash || '',
                  from: tx.from || '',
                  to: tx.to || '',
                  value: tx.value?.toString() || '0',
                  timestamp: Number(block.timestamp),
                  blockNumber: Number(blockNum),
                  transactionType: transactionAnalysis.transactionType,
                  isTokenCreation: transactionAnalysis.isTokenCreation,
                  createdTokenAddress: transactionAnalysis.createdTokenAddress || undefined,
                  tokenAddress: transactionAnalysis.tokenAddress || undefined,
                  tokenName: transactionAnalysis.tokenName || undefined,
                  tokenSymbol: transactionAnalysis.tokenSymbol || undefined,
                  swapDetails: transactionAnalysis.swapDetails,
                  liquidityDetails: transactionAnalysis.liquidityDetails,
                  confidence: transactionAnalysis.confidence,
                  logs: transactionAnalysis.logs
                });

                foundTransactions++;
                if (foundTransactions >= limit) break;
              }
            }
          }
        }

        // Add a small delay to avoid overwhelming the RPC
        if (blocksChecked % 10 === 0) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }

      } catch (blockError) {
        console.error(`Error fetching block ${blockNum}:`, blockError);
        // Continue with next block
        continue;
      }
    }

    // Sort transactions by block number (newest first)
    transactions.sort((a, b) => b.blockNumber - a.blockNumber);

    console.log(`Found ${transactions.length} transactions for address ${address}`);

    return Response.json({
      success: true,
      address,
      chainId,
      transactions: transactions.slice(0, limit),
      blocksChecked: blocksChecked,
      latestBlock: Number(latestBlock),
      totalFound: transactions.length
    });

  } catch (error) {
    console.error('Error fetching transactions:', error);
    
    // Return a more specific error message
    let errorMessage = 'Failed to fetch transactions';
    if (error instanceof Error) {
      errorMessage = error.message;
    }

    return Response.json(
      { 
        error: errorMessage,
        success: false 
      },
      { status: 500 }
    );
  }
}
