# Database Setup Guide for Web3Socials

This guide provides comprehensive instructions for setting up the Web3Socials database on a fresh installation, specifically for Hostinger VPS with CloudPanel and MySQL.

## 📋 Database Schema Overview

The Web3Socials application uses **11 main tables** that are all properly defined and will be created automatically:

### Core Tables

1. **system_settings** - System configuration and chain-specific settings
2. **web3Profile** - Main user profiles with wallet addresses, names, roles, and status
3. **waitingList** - Users waiting for profile approval
4. **componentPositions** - Layout and configuration for profile components
5. **componentImages** - Image storage for profile components

### Feature Tables

6. **profileLikes** - Profile like/favorite system
7. **profileReferrals** - Referral tracking system with codes

### Token Management Tables

8. **token_holder_snapshots** - Metadata for token holder data fetches
9. **token_holders** - Individual token holder addresses and balances
10. **token_holder_jobs** - Background job tracking for token data fetching
11. **balance_check_logs** - Logs for bulk balance checking operations

## 🚀 Quick Setup (Recommended)

### For Windows (Local Development)

```bash
# Run the automated setup script
setup-production-database.bat
```

### For Linux/Ubuntu (Production VPS)

```bash
# Make script executable and run
chmod +x setup-production-database.sh
./setup-production-database.sh
```

## 🔧 Manual Setup Process

If you prefer to run the setup manually:

### 1. Environment Configuration

Ensure your `.env` file is properly configured:

```env
DATABASE_URL=mysql://username:password@localhost:3306/database_name
MYSQL_SSL=false
NEXT_PUBLIC_PROJECT_ID=your_reown_project_id
NODE_ENV=production
```

### 2. Install Dependencies

```bash
npm install
```

### 3. Reset and Create All Tables

```bash
# This will drop all existing tables and recreate them with all required tables
npm run reset-database
```

### 4. Verify Setup

```bash
# Check that all tables were created successfully
npm run check-tables

# Verify chain-specific settings
npm run verify-chain-defaults
```

## 📊 Table Details

### Core Profile System

- **web3Profile**: Stores user wallet addresses, profile names, roles (admin/user), status (new/approved/etc), referral codes
- **componentPositions**: Stores layout configuration for each profile component (hero, social links, etc.)
- **componentImages**: Stores base64 image data with positioning and scaling information

### Token Requirements System

- **token_holder_snapshots**: Tracks when token holder data was fetched
- **token_holders**: Individual holder addresses and balances for token validation
- **token_holder_jobs**: Background job status for fetching token data
- **balance_check_logs**: Audit trail for bulk balance checking operations

### Social Features

- **profileLikes**: User-to-user profile likes/favorites
- **profileReferrals**: Tracks who referred whom using referral codes

### Sniper System

- **sniper_creators**: Stores creator addresses and names for monitoring new token launches
- **sniper_watched_addresses**: Stores specific wallet addresses to watch for token activities
- **sniper_configs**: User-specific sniper configurations including private keys, buy amounts, and preferences

## 🔍 Verification Commands

After setup, you can verify your database:

```bash
# Check all tables exist
npm run check-tables

# Verify chain-specific configuration
npm run verify-chain-defaults

# Test database connection
npm run dev
```

## 🚨 Important Notes for Production

1. **Backup First**: Always backup your existing database before running the reset script
2. **Environment Variables**: Ensure your `.env` file has the correct production database credentials
3. **SSL Configuration**: Set `MYSQL_SSL=false` for local connections or `MYSQL_SSL=true` for remote connections
4. **Address Length**: The schema supports both Ethereum (42 chars) and Solana (44 chars) addresses

## 🛠️ Troubleshooting

### Common Issues

**"Table doesn't exist" errors:**

- Run `npm run check-tables` to see which tables are missing
- Run `npm run reset-database` to recreate all tables

**Connection errors:**

- Verify your `DATABASE_URL` in `.env`
- Check MySQL service is running
- Verify database credentials and permissions

**Migration errors:**

- Ensure you have proper MySQL permissions (CREATE, DROP, ALTER)
- Check MySQL version compatibility (8.0+ recommended)

### Manual Table Creation

If automated setup fails, you can create tables manually using the SQL files in `db/migrations/`:

- `add_token_holder_tables.sql`
- `add_balance_check_logs.sql`
- `fix_contract_address_length.sql`

## 📈 Post-Setup

After successful database setup:

1. **Build the application**: `npm run build`
2. **Start production server**: `npm start`
3. **Create admin user**: Access the application and create your first admin profile
4. **Configure system settings**: Use the admin panel to configure chain-specific settings

Your Web3Socials database is now ready for production use! 🎉
