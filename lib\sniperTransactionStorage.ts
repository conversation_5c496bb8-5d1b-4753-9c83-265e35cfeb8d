import { db } from '@/db/drizzle';
import { sniperTransactions, sniperCreators, sniperWatchedAddresses } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { TransactionAnalysis, TransactionType } from '@/app/api/sniper/transactions/route';

export interface StoredTransactionData {
  sessionId: string;
  userAddress: string;
  chainId: string;
  transactionHash: string;
  blockNumber: number;
  timestamp: number;
  fromAddress: string;
  toAddress?: string;
  value: string;
  transactionAnalysis: TransactionAnalysis;
  creatorInfo?: {
    name: string;
    address: string;
  };
  watchedAddressInfo?: {
    name?: string;
    address: string;
  };
  autoBuyConfig?: {
    enabled: boolean;
    triggered: boolean;
    transactionHash?: string;
    status?: 'SUCCESS' | 'FAILED' | 'PENDING' | 'NOT_TRIGGERED';
  };
  parametersChecked?: {
    [key: string]: {
      checked: boolean;
      value?: any;
      met: boolean;
      reason?: string;
    };
  };
}

/**
 * Store a monitored transaction in the database
 */
export async function storeTransaction(data: StoredTransactionData): Promise<number | null> {
  try {
    // Determine overall parameters met status
    const parametersMet = data.parametersChecked 
      ? Object.values(data.parametersChecked).every(param => param.met)
      : false;

    const result = await db.insert(sniperTransactions).values({
      sessionId: data.sessionId,
      userAddress: data.userAddress,
      chainId: data.chainId,
      transactionHash: data.transactionHash,
      blockNumber: data.blockNumber,
      timestamp: data.timestamp,
      fromAddress: data.fromAddress,
      toAddress: data.toAddress || null,
      value: data.value,
      transactionType: data.transactionAnalysis.transactionType,
      isTokenCreation: data.transactionAnalysis.isTokenCreation ? 'Y' : 'N',
      createdTokenAddress: data.transactionAnalysis.createdTokenAddress || null,
      tokenAddress: data.transactionAnalysis.tokenAddress || null,
      tokenName: data.transactionAnalysis.tokenName || null,
      tokenSymbol: data.transactionAnalysis.tokenSymbol || null,
      creatorName: data.creatorInfo?.name || null,
      watchedAddress: data.watchedAddressInfo?.address || null,
      watchedAddressName: data.watchedAddressInfo?.name || null,
      autoBuyEnabled: data.autoBuyConfig?.enabled ? 'Y' : 'N',
      autoBuyTriggered: data.autoBuyConfig?.triggered ? 'Y' : 'N',
      autoBuyTransactionHash: data.autoBuyConfig?.transactionHash || null,
      autoBuyStatus: data.autoBuyConfig?.status || null,
      parametersChecked: data.parametersChecked || null,
      parametersMet: parametersMet ? 'Y' : 'N',
      confidence: data.transactionAnalysis.confidence || 0,
      swapDetails: data.transactionAnalysis.swapDetails || null,
      liquidityDetails: data.transactionAnalysis.liquidityDetails || null,
      rawLogs: data.transactionAnalysis.logs || null,
    });

    return result[0].insertId;
  } catch (error) {
    console.error('Error storing transaction:', error);
    return null;
  }
}

/**
 * Get creator information by address
 */
export async function getCreatorInfo(address: string, chainId: string): Promise<{ name: string; address: string } | null> {
  try {
    const creator = await db.select()
      .from(sniperCreators)
      .where(
        and(
          eq(sniperCreators.address, address),
          eq(sniperCreators.chainId, chainId),
          eq(sniperCreators.isActive, 'Y')
        )
      )
      .limit(1);

    if (creator.length > 0) {
      return {
        name: creator[0].name,
        address: creator[0].address
      };
    }

    return null;
  } catch (error) {
    console.error('Error getting creator info:', error);
    return null;
  }
}

/**
 * Get watched address information by address
 */
export async function getWatchedAddressInfo(address: string, chainId: string): Promise<{ name?: string; address: string } | null> {
  try {
    const watchedAddress = await db.select()
      .from(sniperWatchedAddresses)
      .where(
        and(
          eq(sniperWatchedAddresses.address, address),
          eq(sniperWatchedAddresses.chainId, chainId),
          eq(sniperWatchedAddresses.isActive, 'Y')
        )
      )
      .limit(1);

    if (watchedAddress.length > 0) {
      return {
        name: watchedAddress[0].name || undefined,
        address: watchedAddress[0].address
      };
    }

    return null;
  } catch (error) {
    console.error('Error getting watched address info:', error);
    return null;
  }
}

/**
 * Update autobuy status for a stored transaction
 */
export async function updateTransactionAutoBuyStatus(
  transactionHash: string,
  autoBuyTransactionHash: string,
  status: 'SUCCESS' | 'FAILED' | 'PENDING'
): Promise<boolean> {
  try {
    await db.update(sniperTransactions)
      .set({
        autoBuyTriggered: 'Y',
        autoBuyTransactionHash,
        autoBuyStatus: status
      })
      .where(eq(sniperTransactions.transactionHash, transactionHash));

    return true;
  } catch (error) {
    console.error('Error updating autobuy status:', error);
    return false;
  }
}

/**
 * Get recent transactions for a session
 */
export async function getSessionTransactions(sessionId: string, limit: number = 50) {
  try {
    const transactions = await db.select()
      .from(sniperTransactions)
      .where(eq(sniperTransactions.sessionId, sessionId))
      .orderBy(sniperTransactions.timestamp)
      .limit(limit);

    return transactions;
  } catch (error) {
    console.error('Error getting session transactions:', error);
    return [];
  }
}
