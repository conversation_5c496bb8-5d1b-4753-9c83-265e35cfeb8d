const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function migrateSniperLogs() {
  let connection;

  try {
    // Create connection using the same config as the app
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'web3user',
      password: 'web3password',
      database: 'web3profiles'
    });

    console.log('Connected to database');

    // Read and execute the migration SQL
    const migrationPath = path.join(__dirname, '..', 'db', 'migrations', 'add_sniper_logs.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('Executing sniper logs migration...');
    await connection.execute(migrationSQL);

    console.log('✅ Sniper logs table migration completed successfully!');
    console.log('📋 Created table:');
    console.log('   • sniper_logs - Sniper monitoring and debugging logs');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
migrateSniperLogs();
