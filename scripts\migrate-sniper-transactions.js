const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');

async function migrateSniperTransactions() {
  let connection;

  try {
    // Create connection using the same config as the app
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'web3user',
      password: 'web3password',
      database: 'web3profiles'
    });

    console.log('Connected to database');

    // Read and execute the migration SQL
    const migrationPath = path.join(__dirname, '..', 'db', 'migrations', 'add_sniper_transactions.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('Executing sniper transactions migration...');
    await connection.execute(migrationSQL);

    console.log('✅ Sniper transactions table migration completed successfully!');
    console.log('📋 Created table:');
    console.log('   • sniper_transactions - Comprehensive transaction monitoring and analysis');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('Database connection closed');
    }
  }
}

// Run the migration
migrateSniperTransactions();
